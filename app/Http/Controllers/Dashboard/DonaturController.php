<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Donatur;
use App\Models\User;
use Illuminate\Http\Request;

class DonaturController extends Controller
{
    public function index(Request $request)
    {
        $query = Donatur::query()->with('users');

        // Apply filters if provided
        if ($request->filled('name')) {
            $query->where('name', 'ilike', '%' . $request->name . '%');
        }

        if ($request->filled('manager')) {
            if ($request->manager === 'null') {
                // Filter for donaturs with no manager
                $query->whereDoesntHave('users', function ($q) {
                    $q->whereNotNull('manager');
                });
            } else {
                // Filter for donaturs with the specified manager
                // Get only donaturs where ALL associated users have the specified manager
                $donatursWithOtherManagers = Donatur::whereHas('users', function ($q) use ($request) {
                    $q->where('manager', 'not ilike', $request->manager);
                })->pluck('id');
                
                $query->whereHas('users', function ($q) use ($request) {
                    $q->where('manager', 'ilike', $request->manager);
                });
                
                if ($donatursWithOtherManagers->count() > 0) {
                    $query->whereNotIn('id', $donatursWithOtherManagers);
                }
            }
        }

        if ($request->filled('penghubung')) {
            $query->whereHas('users', function($q) use ($request) {
                $q->where('users.id', $request->penghubung);
            });
        }

        $donaturs = $query->orderBy('created_at', 'desc')->paginate(10)->withQueryString();

        if ($request->ajax()) {
            return view('dashboard.donaturs.table', compact('donaturs'))->render();
        }

        return view('dashboard.donaturs.index', compact('donaturs'));
    }

    /**
     * Standardize a phone number for storage.
     *
     * @param  string|null  $phone
     * @return string|null
     */
    private function standardizePhoneNumber(?string $phone): ?string
    {
        if (empty($phone)) {
            return null;
        }

        // Remove all non-numeric characters
        $number = preg_replace('/[^0-9]/', '', $phone);

        // Handle different prefixes
        if (str_starts_with($number, '0')) {
            $number = '62' . substr($number, 1);
        } elseif (!str_starts_with($number, '62')) {
            $number = '62' . $number;
        }

        // Add the '+' prefix
        return '+' . $number;
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'social_media' => 'nullable|array',
        ]);

        // Standardize phone number
        if (isset($validated['phone'])) {
            $validated['phone'] = $this->standardizePhoneNumber($validated['phone']);
        }

        // Create the donatur
        $donatur = Donatur::create($validated);

        return response()->json([
            'message' => 'Donatur created successfully',
            'donatur' => $donatur
        ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Donatur $donatur)
    {
        // Eager load the users relationship with only the fields we need
        $donatur->load(['users' => function ($query) {
            $query->select('users.id', 'users.name', 'users.manager');
        }]);

        // Ensure social_media is properly cast to an array
        if ($donatur->social_media === null) {
            $donatur->social_media = [];
        }

        return response()->json($donatur);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Donatur $donatur)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'description' => 'nullable|string',
            'social_media' => 'nullable|array',
        ]);

        // Standardize phone number
        if (isset($validated['phone'])) {
            $validated['phone'] = $this->standardizePhoneNumber($validated['phone']);
        }

        // Update the donatur
        $donatur->update($validated);

        return response()->json([
            'message' => 'Donatur updated successfully',
            'donatur' => $donatur
        ]);
    }

    public function destroy(Donatur $donatur)
    {
        $donatur->delete();

        return response()->json(['message' => 'Donatur deleted successfully']);
    }

    public function search(Request $request)
    {
        $query = $request->input('q');
        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }
        $donaturs = Donatur::where('name', 'ilike', "%{$query}%")
            ->select('id', 'name')
            ->orderBy('name')
            ->limit(10)
            ->get();
        return response()->json($donaturs);
    }

    /**
     * Search for users by name or email.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function searchUsers(Request $request)
    {
        $query = $request->input('q');

        if (empty($query) || strlen($query) < 2) {
            return response()->json([]);
        }

        $users = User::where('name', 'ilike', "%{$query}%")
            ->whereIn('role', ['admin', 'staff']) // Only admin and staff roles
            ->select('id', 'name', 'manager')
            ->limit(10)
            ->get();

        return response()->json($users);
    }

    /**
     * Get a user by ID.
     *
     * @param  \App\Models\User  $user
     * @return \Illuminate\Http\Response
     */
    public function getUser(User $user)
    {
        // Only return the user if they are admin or staff
        if (!in_array($user->role, ['admin', 'staff'])) {
            return response()->json(['error' => 'User not found'], 404);
        }

        return response()->json($user->only(['id', 'name', 'manager']));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\Donatur  $donatur
     * @return \Illuminate\Http\Response
     */
    public function edit(Donatur $donatur)
    {
        // Load the users relationship with all needed fields
        $donatur->load(['users' => function ($query) {
            $query->select('users.id', 'users.name', 'users.email', 'users.manager');
        }]);

        // Make sure social_media is an array
        if (!is_array($donatur->social_media)) {
            $donatur->social_media = json_decode($donatur->social_media, true) ?: [];
        }

        return response()->json($donatur);
    }
}
