/* Basic styling for filter elements */
.filter-form-donasi input,
.filter-form-donasi select,
.filter-form-donasi button {
    box-sizing: border-box !important;
    font-size: 0.875rem !important;
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
}

/* Component-specific fixes */
.filter-form-donasi [x-data] input,
.filter-form-donasi [x-data] div[x-show="selectedUser"],
.filter-form-donasi [x-data] div[x-show="selectedDonatur"],
.filter-form-donasi [x-data] div[x-show="selectedProgram"] {
    box-sizing: border-box !important;
    display: flex !important;
    align-items: center !important;
}

/* Fix for the selected item display */
.filter-form-donasi [x-data] div[x-show="selectedUser"],
.filter-form-donasi [x-data] div[x-show="selectedDonatur"],
.filter-form-donasi [x-data] div[x-show="selectedProgram"] {
    display: flex !important;
    align-items: center !important;
}

/* Fix for the search button */
.filter-form-donasi button[type="submit"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    padding: 0.25rem 0.5rem !important;
}

/* Fix for custom component wrappers */
.filter-form-donasi > div > div,
.filter-form-donasi > div > [x-data] {
    height: 28px !important;
}

/* Fix for dropdown arrows in selects */
.filter-form-donasi .relative .pointer-events-none {
    height: 28px !important;
    display: flex !important;
    align-items: center !important;
}
