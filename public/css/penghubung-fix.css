/* Fix for the gray area in the penghubung component */
.filter-form-donasi .relative {
    background-color: transparent !important;
}

/* Hide any gray backgrounds */
.filter-form-donasi [x-data] div[x-show="!selectedUser"] {
    background-color: transparent !important;
}

/* Fix for the penghubung component */
.filter-form-donasi x-penghubung,
.filter-form-donasi x-penghubung > div,
.filter-form-donasi x-penghubung > div > div {
    background-color: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

/* Make sure input is visible */
.filter-form-donasi x-penghubung input {
    display: block !important;
    background-color: white !important;
}

/* Fix for the selected user display */
.filter-form-donasi x-penghubung div[x-show="selectedUser"] {
    background-color: white !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
}
