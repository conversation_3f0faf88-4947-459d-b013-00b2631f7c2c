@props(['initialValue' => null, 'placeholder' => 'Penghubung', 'id' => 'penghubung-input'])

<div 
    x-data="{
        searchTerm: '',
        selectedUser: null,
        showDropdown: false,
        users: [],
        loading: false,
        
        init() {
            if ('{{ $initialValue }}') {
                this.fetchUserById('{{ $initialValue }}');
            }
            
            document.addEventListener('click', (e) => {
                if (!this.$el.contains(e.target)) {
                    this.showDropdown = false;
                }
            });
        },
        
        async fetchUserById(userId) {
            if (!userId) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/donatur/get-user/${userId}`);
                
                if (response.ok) {
                    const user = await response.json();
                    this.selectedUser = user;
                    this.$dispatch('penghubung-selected', { user });
                }
            } catch (error) {
                this.loading = false;
            }
        },
        
        async search() {
            if (this.searchTerm.length < 2) return;
            
            this.loading = true;
            try {
                const response = await fetch(`/users/search?q=${encodeURIComponent(this.searchTerm)}`);
                
                if (response.ok) {
                    this.users = await response.json();
                    this.showDropdown = true;
                }
            } catch (error) {
                this.users = [];
            } finally {
                this.loading = false;
            }
        },
        
        selectUser(user) {
            this.selectedUser = user;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('penghubung-selected', { user });
        },
        
        clearSelection() {
            this.selectedUser = null;
            this.searchTerm = '';
            this.showDropdown = false;
            this.$dispatch('penghubung-cleared');
        }
    }"
    {{ $attributes->merge(['class' => 'relative']) }}
>
    <div class="relative">
        <input 
            x-show="!selectedUser"
            type="text" 
            id="{{ $id }}"
            x-model="searchTerm"
            @input.debounce.300ms="search()"
            @focus="if(searchTerm.length >= 2) search()"
            placeholder="{{ $placeholder }}"
            class="block w-full px-2 py-1 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
        >
        
        <div 
            x-show="selectedUser"
            class="flex items-center justify-between px-2 py-1 text-sm rounded-md border border-gray-300 bg-gray-50"
        >
            <span x-text="selectedUser ? selectedUser.name : ''" class="truncate"></span>
            <button 
                @click="clearSelection()"
                type="button"
                class="ml-1 text-gray-400 hover:text-gray-600 focus:outline-none"
            >
                <x-icon name="x" width="16" height="16" class="inline-block" />
            </button>
        </div>
        
        <div 
            x-show="loading"
            class="absolute right-2 top-1/2 transform -translate-y-1/2"
        >
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
        </div>
        
        <div 
            x-show="showDropdown && users.length > 0"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 max-h-60 overflow-y-auto"
        >
            <ul class="py-1">
                <template x-for="user in users" :key="user.id">
                    <li 
                        @click="selectUser(user)"
                        class="px-3 py-2 text-sm hover:bg-gray-100 cursor-pointer flex items-center justify-between"
                    >
                        <span x-text="user.name" class="truncate"></span>
                        <span x-text="user.manager || 'No Manager'" class="text-xs text-gray-500"></span>
                    </li>
                </template>
            </ul>
        </div>
        
        <div 
            x-show="showDropdown && searchTerm && users.length === 0 && !loading"
            class="absolute z-10 mt-1 w-full bg-white shadow-lg rounded-md border border-gray-200 py-2 px-3 text-sm text-gray-500"
        >
            No users found
        </div>
    </div>
</div>
