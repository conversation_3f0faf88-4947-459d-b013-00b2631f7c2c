@props(['initialValue' => null, 'placeholder' => 'Select Program', 'id' => 'program-form-input', 'multiple' => false])

<div 
    x-data="programForm('{{ $initialValue }}', {{ $multiple ?? 'false' }})"
    x-init="window._programFormId = $el.getAttribute('id') || '{{ $id }}'"
    id="{{ $id }}"
    {{ $attributes->merge(['class' => 'relative']) }}
>
    <div class="relative">
        <!-- Search input (shown when adding new programs) -->
        <input 
            type="text" 
            id="{{ $id }}"
            x-model="searchTerm"
            @input="searchPrograms()"
            @focus="if(searchTerm.length >= 2) searchPrograms()"
            placeholder="{{ $placeholder }}"
            class="block w-full px-3 py-2 text-sm rounded-md border border-gray-300 shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-30 transition duration-150"
            x-bind:class="{'border-red-300 focus:ring-red-500': errors && errors.program_ids}"
            x-show="multiple || selectedPrograms.length === 0"
        >
        
        <!-- Loading indicator -->
        <div x-show="loading" class="absolute right-3 top-2.5">
            <x-icon name="spinner" width="16" height="16" class="animate-spin text-gray-400" />
        </div>
        
        <!-- Results dropdown -->
        <div 
            x-show="showResults" 
            class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
        >
            <div class="py-1">
                <template x-for="program in filteredPrograms" :key="program.id">
                    <button
                        type="button"
                        @click="selectProgram(program)"
                        class="w-full px-4 py-2 text-sm text-left text-gray-700 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                    >
                        <span x-text="program.name" class="truncate"></span>
                    </button>
                </template>
            </div>
        </div>
    </div>
    
    <!-- Selected programs display -->
    <template x-if="!multiple && selectedPrograms.length === 1">
        <div class="flex items-center justify-between w-full px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50">
            <div>
                <span x-text="selectedPrograms[0] ? selectedPrograms[0].name : ''"></span>
                <span x-show="selectedPrograms[0] && selectedPrograms[0].manager" class="text-xs text-gray-500 ml-1" x-text="selectedPrograms[0] && selectedPrograms[0].manager ? '(' + selectedPrograms[0].manager + ')' : ''"></span>
            </div>
            <button type="button" @click="removeProgram(0)" class="text-gray-400 hover:text-gray-600">
                <x-icon name="x" width="16" height="16" />
            </button>
        </div>
    </template>
    <template x-if="multiple && selectedPrograms.length > 0">
        <div class="space-y-2">
            <template x-for="(program, index) in selectedPrograms" :key="program.id">
                <div class="flex items-center justify-between w-full px-3 py-2 text-sm rounded-md border border-gray-300 bg-gray-50">
                    <div>
                        <span x-text="program.name"></span>
                        <span x-show="program.manager" class="text-xs text-gray-500 ml-1" x-text="program.manager ? '(' + program.manager + ')' : ''"></span>
                    </div>
                    <button type="button" @click="removeProgram(index)" class="text-gray-400 hover:text-gray-600">
                        <x-icon name="x" width="16" height="16" />
                    </button>
                </div>
            </template>
        </div>
    </template>
    
    <!-- Error message -->
    <div x-show="errors && errors.program_ids" x-text="errors && errors.program_ids ? errors.program_ids : ''" class="mt-1 text-sm text-red-500"></div>
</div>

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('programForm', (initialValue, multiple = false) => ({
            multiple: multiple,
            selectedPrograms: [],
            searchTerm: '',
            showResults: false,
            filteredPrograms: [],
            searchTimeout: null,
            loading: false,
            selectedPrograms: [],
            errors: {},
            
            init() {
                // Initialize selectedPrograms if initialValue exists
                if (initialValue && initialValue !== 'null') {
                    this.fetchProgramsByIds(initialValue.split(','));
                }
                
                // Listen for init-with-programs event
                this.$el.addEventListener('init-with-programs', (event) => {
                    if (event.detail && event.detail.programIds) {
                        this.fetchProgramsByIds(event.detail.programIds);
                    }
                });
                
                // Listen for set-selected-programs event
                this.$el.addEventListener('set-selected-programs', (event) => {
                    if (event.detail && event.detail.programs) {
                        this.selectedPrograms = event.detail.programs;
                    }
                });
                
                // Listen for reset event
                window.addEventListener('reset-program-form', () => {
                    this.selectedPrograms = [];
                    this.searchTerm = '';
                    this.showResults = false;
                    this.filteredPrograms = [];
                });
                
                // Listen for reset-form event (direct on element)
                this.$el.addEventListener('reset-form', () => {
                    this.selectedPrograms = [];
                    this.searchTerm = '';
                    this.showResults = false;
                    this.filteredPrograms = [];
                });
                
                // Close dropdown when clicking outside
                document.addEventListener('click', (e) => {
                    if (!this.$el.contains(e.target)) {
                        this.showResults = false;
                    }
                });
                
                // Get errors from parent component if available
                try {
                    if (typeof $data !== 'undefined' && $data.errors) {
                        this.errors = $data.errors;
                    }
                } catch (e) {
                }
            },
            
            async fetchProgramsByIds(programIds) {
                if (!programIds || !programIds.length) return;
                
                this.loading = true;
                try {
                    const response = await fetch(`/programs/get-multiple?ids=${programIds.join(',')}`);
                    
                    if (response.ok) {
                        this.selectedPrograms = await response.json();
                    }
                } catch (error) {
                    console.error('Error fetching programs:', error);
                } finally {
                    this.loading = false;
                }
            },
            
            searchPrograms() {
                if (this.searchTerm.length < 2) {
                    this.showResults = false;
                    return;
                }
                
                // Clear previous timeout
                if (this.searchTimeout) clearTimeout(this.searchTimeout);
                
                // Set a new timeout (300ms debounce)
                this.searchTimeout = setTimeout(async () => {
                    this.loading = true;
                    try {
                        const response = await fetch(`/programs/search?q=${encodeURIComponent(this.searchTerm)}`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                            },
                            credentials: 'same-origin'
                        });
                        
                        if (response.ok) {
                            this.filteredPrograms = await response.json();
                            this.showResults = this.filteredPrograms.length > 0;
                        } else {
                            console.error('Error searching programs:', response.status);
                            this.filteredPrograms = [];
                            this.showResults = false;
                        }
                    } catch (error) {
                        console.error('Error searching programs:', error);
                        this.filteredPrograms = [];
                        this.showResults = false;
                    } finally {
                        this.loading = false;
                    }
                }, 300);
            },
            
            selectProgram(program) {
                if (this.multiple) {
                    if (!this.selectedPrograms.some(p => p.id === program.id)) {
                        this.selectedPrograms.push(program);
                        this.$dispatch('input', this.selectedPrograms);
                    }
                } else {
                    this.selectedPrograms = [program];
                    this.$dispatch('input', program);
                }
                this.showResults = false;
            },
            
            removeProgram(index) {
                if (this.multiple) {
                    this.selectedPrograms.splice(index, 1);
                    this.$dispatch('input', this.selectedPrograms);
                } else {
                    this.selectedPrograms = [];
                    this.$dispatch('input', { name: '' });
                }
            },
            
            getProgramIds() {
                return this.selectedPrograms.map(p => p.id);
            }
        }));
    });
</script>